# DataPermissionInterceptor 优化说明

## 问题描述

原有的数据权限拦截器在处理复杂的SQL查询时存在问题，特别是涉及UNION查询和子查询的场景。

### 原始错误
```
### The error may involve defaultParameterMap 
### The error occurred while setting parameters
### SQL: SELECT goodsld, goodsName, SUM(amount) AS amount FROM (SELECT g.id AS goodsld, g.goods_name AS goodsName, COALESCE(SUM(r.reconciliation_amount), 0) AS amount FROM t_goods g INNER JOIN t_project p ON g.id = p.goods_id LEFT JOIN t_reconciliation r ON p.id = r.project_id AND r.del = 0 AND r.state = 5 AND r.type = 2 WHERE p.state IN (?, ?) AND g.del = 0 AND p.del = 0 GROUP BY g.id, g.goods_name UNION ALL SELECT g.id AS goodsld, g.goods_name AS goodsName, COALESCE(SUM(pi.total_reconciled_sales_amount), 0) AS amount FROM t_goods g INNER JOIN t_project p ON g.id = p.goods_id LEFT JOIN t_project_inception pi ON p.id = pi.project_id AND pi.del = 0 WHERE p.state IN (?, ?) AND g.del = 0 AND p.del = 0 GROUP BY g.id, g.goods_name) AS combined_results WHERE tenant_id = 100000000001 GROUP BY goodsld, goodsName ORDER BY amount DESC 
### Cause: java.sql.SQLSyntaxErrorException: Unknown column 'tenant_id' in 'where clause'
```

### 问题分析
1. **错误位置**: 拦截器将`tenant_id`条件添加到了外层查询的WHERE子句中
2. **根本原因**: 外层查询的结果集是子查询的别名`combined_results`，其中不包含`tenant_id`字段
3. **正确做法**: 应该将`tenant_id`条件添加到子查询中的实际表上

## 优化方案

### 核心改进
1. **递归处理**: 支持复杂的嵌套查询结构
2. **智能识别**: 区分实际表和子查询，只对实际表添加条件
3. **UNION支持**: 正确处理UNION查询中的每个分支

### 新增方法

#### 1. `processSelectBody(SelectBody selectBody, Long tenantId)`
递归处理所有类型的查询体，包括：
- `PlainSelect`: 普通查询
- `SetOperationList`: UNION查询

#### 2. `processPlainSelect(PlainSelect plainSelect, Long tenantId)`
处理单个查询，为主表添加tenant_id条件，并递归处理子查询

#### 3. `shouldAddTenantCondition(PlainSelect plainSelect)`
判断是否应该添加tenant_id条件：
- 检查FROM子句是否为实际表（Table类型）
- 如果是子查询（SubSelect类型），则不在此处添加条件

#### 4. `processSubQueries(PlainSelect plainSelect, Long tenantId)`
递归处理子查询：
- FROM子句中的子查询
- JOIN子句中的子查询

## 处理逻辑

### 原始逻辑
```java
// 只处理最外层的PlainSelect
if (selectStatement.getSelectBody() instanceof PlainSelect plainSelect) {
    // 直接添加tenant_id条件到WHERE子句
    plainSelect.setWhere(andExpression);
}
```

### 优化后逻辑
```java
// 递归处理所有SelectBody
processSelectBody(selectStatement.getSelectBody(), tenantId);

// 在processSelectBody中：
if (selectBody instanceof PlainSelect plainSelect) {
    processPlainSelect(plainSelect, tenantId);
} else if (selectBody instanceof SetOperationList setOperationList) {
    // 处理UNION查询的每个分支
    for (SelectBody body : setOperationList.getSelects()) {
        processSelectBody(body, tenantId);
    }
}
```

## 示例对比

### 原始SQL
```sql
SELECT goodsId, goodsName, SUM(amount) AS amount 
FROM (
    SELECT g.id AS goodsId, g.goods_name AS goodsName, SUM(r.reconciliation_amount) AS amount 
    FROM t_goods g 
    INNER JOIN t_project p ON g.id = p.goods_id 
    WHERE p.state IN (1, 2) AND g.del = 0 
    GROUP BY g.id, g.goods_name 
    UNION ALL 
    SELECT g.id AS goodsId, g.goods_name AS goodsName, SUM(pi.total_reconciled_sales_amount) AS amount 
    FROM t_goods g 
    INNER JOIN t_project p ON g.id = p.goods_id 
    WHERE p.state IN (1, 2) AND g.del = 0 
    GROUP BY g.id, g.goods_name
) AS combined_results 
GROUP BY goodsId, goodsName 
ORDER BY amount DESC
```

### 错误的处理结果（原逻辑）
```sql
-- 在外层查询添加tenant_id条件，导致错误
SELECT goodsId, goodsName, SUM(amount) AS amount 
FROM (...) AS combined_results 
WHERE tenant_id = 100000000001  -- ❌ 错误：combined_results中没有tenant_id字段
GROUP BY goodsId, goodsName 
ORDER BY amount DESC
```

### 正确的处理结果（优化后）
```sql
SELECT goodsId, goodsName, SUM(amount) AS amount 
FROM (
    SELECT g.id AS goodsId, g.goods_name AS goodsName, SUM(r.reconciliation_amount) AS amount 
    FROM t_goods g 
    INNER JOIN t_project p ON g.id = p.goods_id 
    WHERE p.state IN (1, 2) AND g.del = 0 AND g.tenant_id = 100000000001  -- ✅ 正确
    GROUP BY g.id, g.goods_name 
    UNION ALL 
    SELECT g.id AS goodsId, g.goods_name AS goodsName, SUM(pi.total_reconciled_sales_amount) AS amount 
    FROM t_goods g 
    INNER JOIN t_project p ON g.id = p.goods_id 
    WHERE p.state IN (1, 2) AND g.del = 0 AND g.tenant_id = 100000000001  -- ✅ 正确
    GROUP BY g.id, g.goods_name
) AS combined_results 
GROUP BY goodsId, goodsName 
ORDER BY amount DESC
```

## 测试验证

可以运行`SqlProcessingExample`类来验证优化效果：

```bash
java -cp ... com.zhihaoscm.service.config.intceptor.SqlProcessingExample
```

## 兼容性

- ✅ 向后兼容：原有的简单查询仍然正常工作
- ✅ 性能优化：避免了错误的SQL执行
- ✅ 功能增强：支持复杂的UNION和子查询场景

## 注意事项

1. **排除表列表**: 保持原有的排除表逻辑不变
2. **错误处理**: 保持原有的异常捕获和日志记录
3. **租户上下文**: 保持原有的租户ID获取逻辑
