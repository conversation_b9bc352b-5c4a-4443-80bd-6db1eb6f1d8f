package com.zhihaoscm.service.config.intceptor;

import lombok.extern.slf4j.Slf4j;
import net.sf.jsqlparser.expression.LongValue;
import net.sf.jsqlparser.expression.operators.conditional.AndExpression;
import net.sf.jsqlparser.expression.operators.relational.EqualsTo;
import net.sf.jsqlparser.parser.CCJSqlParserUtil;
import net.sf.jsqlparser.schema.Column;
import net.sf.jsqlparser.schema.Table;
import net.sf.jsqlparser.statement.Statement;
import net.sf.jsqlparser.statement.select.FromItem;
import net.sf.jsqlparser.statement.select.Join;
import net.sf.jsqlparser.statement.select.PlainSelect;
import net.sf.jsqlparser.statement.select.Select;
import net.sf.jsqlparser.statement.select.SelectBody;
import net.sf.jsqlparser.statement.select.SetOperationList;
import net.sf.jsqlparser.statement.select.SubSelect;

import java.util.List;

/**
 * SQL处理示例类，展示如何处理复杂的UNION查询和子查询
 */
@Slf4j
public class SqlProcessingExample {

    /**
     * 演示如何处理复杂的UNION查询
     */
    public static void main(String[] args) {
        // 原始的复杂SQL（类似于报错的SQL）
        String originalSql = """
            SELECT goodsId, goodsName, SUM(amount) AS amount 
            FROM (
                SELECT g.id AS goodsId, g.goods_name AS goodsName, COALESCE(SUM(r.reconciliation_amount), 0) AS amount 
                FROM t_goods g 
                INNER JOIN t_project p ON g.id = p.goods_id 
                LEFT JOIN t_reconciliation r ON p.id = r.project_id AND r.del = 0 AND r.state = 5 AND r.type = 2 
                WHERE p.state IN (1, 2) AND g.del = 0 AND p.del = 0 
                GROUP BY g.id, g.goods_name 
                UNION ALL 
                SELECT g.id AS goodsId, g.goods_name AS goodsName, COALESCE(SUM(pi.total_reconciled_sales_amount), 0) AS amount 
                FROM t_goods g 
                INNER JOIN t_project p ON g.id = p.goods_id 
                LEFT JOIN t_project_inception pi ON p.id = pi.project_id AND pi.del = 0 
                WHERE p.state IN (1, 2) AND g.del = 0 AND p.del = 0 
                GROUP BY g.id, g.goods_name
            ) AS combined_results 
            GROUP BY goodsId, goodsName 
            ORDER BY amount DESC
            """;

        try {
            log.info("原始SQL: {}", originalSql);
            
            Statement statement = CCJSqlParserUtil.parse(originalSql);
            if (statement instanceof Select selectStatement) {
                Long tenantId = 100000000001L;
                processSelectBody(selectStatement.getSelectBody(), tenantId);
                
                String processedSql = selectStatement.toString();
                log.info("处理后的SQL: {}", processedSql);
                
                // 验证处理结果
                validateProcessedSql(processedSql, tenantId);
            }
        } catch (Exception e) {
            log.error("SQL处理失败", e);
        }
    }

    /**
     * 递归处理SelectBody，支持复杂的UNION查询和子查询
     */
    private static void processSelectBody(SelectBody selectBody, Long tenantId) {
        if (selectBody instanceof PlainSelect plainSelect) {
            processPlainSelect(plainSelect, tenantId);
        } else if (selectBody instanceof SetOperationList setOperationList) {
            for (SelectBody body : setOperationList.getSelects()) {
                processSelectBody(body, tenantId);
            }
        }
    }

    /**
     * 处理PlainSelect，为主表添加tenant_id条件
     */
    private static void processPlainSelect(PlainSelect plainSelect, Long tenantId) {
        if (shouldAddTenantCondition(plainSelect)) {
            EqualsTo equalsTo = new EqualsTo();
            equalsTo.setLeftExpression(new Column("tenant_id"));
            equalsTo.setRightExpression(new LongValue(tenantId));

            if (plainSelect.getWhere() == null) {
                plainSelect.setWhere(equalsTo);
            } else {
                AndExpression andExpression = new AndExpression(
                        plainSelect.getWhere(), equalsTo);
                plainSelect.setWhere(andExpression);
            }
        }

        processSubQueries(plainSelect, tenantId);
    }

    /**
     * 判断是否应该为当前查询添加tenant_id条件
     */
    private static boolean shouldAddTenantCondition(PlainSelect plainSelect) {
        FromItem fromItem = plainSelect.getFromItem();
        if (fromItem instanceof Table table) {
            String tableName = table.getName().toLowerCase();
            // 检查是否是需要添加tenant_id的表
            return tableName.startsWith("t_") && !isExcludedTable(tableName);
        }
        return false;
    }

    /**
     * 检查是否是排除的表
     */
    private static boolean isExcludedTable(String tableName) {
        String[] excludedTables = {
            "t_customer", "t_bank_role", "t_bank_user", "t_contract_record",
            "t_dept", "t_file", "t_person", "t_seal"
        };
        
        for (String excluded : excludedTables) {
            if (tableName.equals(excluded)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 递归处理子查询
     */
    private static void processSubQueries(PlainSelect plainSelect, Long tenantId) {
        FromItem fromItem = plainSelect.getFromItem();
        if (fromItem instanceof SubSelect subSelect) {
            processSelectBody(subSelect.getSelectBody(), tenantId);
        }

        List<Join> joins = plainSelect.getJoins();
        if (joins != null) {
            for (Join join : joins) {
                FromItem rightItem = join.getRightItem();
                if (rightItem instanceof SubSelect subSelect) {
                    processSelectBody(subSelect.getSelectBody(), tenantId);
                }
            }
        }
    }

    /**
     * 验证处理后的SQL是否正确
     */
    private static void validateProcessedSql(String processedSql, Long tenantId) {
        // 检查是否正确添加了tenant_id条件
        if (processedSql.contains("tenant_id = " + tenantId)) {
            log.info("✓ 成功添加tenant_id条件");
        } else {
            log.warn("✗ 未找到tenant_id条件");
        }

        // 检查是否避免了在外层查询添加tenant_id
        String[] lines = processedSql.split("\n");
        boolean foundOuterTenantId = false;
        boolean inSubQuery = false;
        
        for (String line : lines) {
            if (line.contains("FROM (") || line.contains("SELECT")) {
                inSubQuery = true;
            }
            if (line.contains(") AS combined_results")) {
                inSubQuery = false;
            }
            if (!inSubQuery && line.contains("tenant_id")) {
                foundOuterTenantId = true;
                break;
            }
        }
        
        if (!foundOuterTenantId) {
            log.info("✓ 正确避免了在外层查询添加tenant_id");
        } else {
            log.warn("✗ 在外层查询中错误添加了tenant_id");
        }
    }
}
